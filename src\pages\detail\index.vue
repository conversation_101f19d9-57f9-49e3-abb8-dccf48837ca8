<template>
  <div class="detail-page">
    <!-- 头部导航 -->
    <van-nav-bar
      :title="pageTitle"
      left-arrow
      @click-left="handleBack"
      fixed
      class="detail-nav"
    >
      <template #right>
        <van-icon name="wap-nav" size="20" @click="showMenu = true" class="menu-icon" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" vertical size="24px">
      <template #icon>
        <van-icon name="photo" size="24" />
      </template>
      {{ $t('detail.loading') }}
    </van-loading>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <van-empty image="error" :description="error">
        <van-button type="primary" size="small" @click="retryLoad">
          {{ $t('common.retry') }}
        </van-button>
      </van-empty>
    </div>

    <!-- 主要内容区域 -->
    <div v-else class="detail-content">
      <!-- BOM Canvas 区域 -->
      <div v-if="currentContentType === 'bom'" class="bom-section">
        <BomCanvas :params="bomCanvasParams" :target-part="targetPart" ref="bomCanvasRef" />
      </div>

      <!-- 产品信息区域 -->
      <div v-else-if="currentContentType === 'product'" class="product-section">
        <ProductInfo :product-data="currentProduct" :loading="productLoading" :error="productError"
          @add-to-cart="handleAddToCart" @add-to-favorites="handleAddToFavorites" @retry="retryLoadProduct" />
      </div>

      <!-- 文件预览区域 -->
      <div v-else-if="currentContentType === 'file'" class="file-section">
        <!-- <FilePreview 
          :file-path="currentFilePath"
          :file-type="currentFileType"
        /> -->
      </div>

      <!-- 默认提示 -->
      <div v-else class="empty-section">
        <van-empty :description="$t('detail.selectFromMenu')" image="search" />
      </div>
    </div>

    <!-- 菜单抽屉 -->
    <van-popup v-model:show="showMenu" position="left" :style="{ width: '80%', height: '100%' }" :lock-scroll="false">
      <div class="menu-container">
        <div class="menu-header">
          <span class="menu-title">{{ $t('detail.menu') }}</span>
          <van-icon name="cross" size="18" @click="showMenu = false" />
        </div>
        <MobileMenu
          ref="mobileMenuRef"
          :menu-data="menuData"
          :loading="loading"
          :expand-to-path="menuExpandPath"
          :highlight-item-id="menuHighlightId"
          @menu-click="handleMenuClick"
        />
      </div>
    </van-popup>

    <!-- 底部操作栏 -->
    <div v-if="showBottomActions" class="bottom-actions">
      <van-button v-if="currentContentType === 'bom'" type="primary" size="small" icon="description"
        @click="handlePdfExport">
        {{ $t('detail.exportPdf') }}
      </van-button>

      <van-button v-if="canExport" type="default" size="small" icon="records" @click="handleXlsxExport">
        {{ $t('detail.exportXlsx') }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, inject, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { showNotify } from 'vant'
import apiService from '@/utils/api'

// 组件导入
import BomCanvas from '@/components/BomCanvas.vue'
import MobileMenu from './components/MobileMenu.vue'
import ProductInfo from './components/ProductInfo.vue'
// import FilePreview from './components/FilePreview.vue'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// 注入全局变量 - 参考PC端的base_downFileByPath_url
const base_downFileByPath_url = inject('$base_downFileByPath_url')

// 响应式数据
const loading = ref(true)
const error = ref('')
const showMenu = ref(false)
const menuData = ref([])
const currentContentType = ref('') // 'bom', 'product', 'file', ''
const currentFilePath = ref('')
const currentFileType = ref('')
const currentProduct = ref(null)
const productLoading = ref(false)
const productError = ref('')
const targetPart = ref(null)
const bomCanvasRef = ref(null)
const mobileMenuRef = ref(null)

// 菜单自动展开控制
const menuExpandPath = ref('') // 需要展开到的文件路径
const menuHighlightId = ref('') // 需要高亮的菜单项ID

// BOM Canvas 参数
const bomCanvasParams = reactive({
  filePath: '',
  bomId: '',
  materielCode: '',
  materielName: '',
  materielNameEn: ''
})

// 计算属性
const pageTitle = computed(() => {
  if (currentProduct.value) {
    return currentProduct.value.materielNameEn || currentProduct.value.materielName || t('detail.productDetail')
  }
  if (currentContentType.value === 'bom') {
    return bomCanvasParams.materielNameEn || bomCanvasParams.materielName || t('detail.bomChart')
  }
  return t('detail.productDetail')
})

const showBottomActions = computed(() => {
  return currentContentType.value === 'bom' || canExport.value
})

const canExport = computed(() => {
  return currentContentType.value === 'bom' && bomCanvasParams.bomId
})

// 方法定义
const handleBack = () => {
  router.back()
}

const handleMenuClick = (item) => {
  showMenu.value = false

  // 处理菜单项
  processMenuItem(item)

  // 如果有文件路径，同步菜单展开状态
  if (item.filePath) {
    menuExpandPath.value = item.filePath
    menuHighlightId.value = item.id || ''
  }
}

const processMenuItem = (item) => {
  const filePath = item.filePath

  if (!filePath) {
    showNotify({ type: 'warning', message: t('detail.invalidFile') })
    return
  }

  // 判断文件类型并处理
  if (filePath.includes('.pdf') && !filePath.includes('/manual') && !filePath.includes('/video')) {
    // BOM 图表
    handleBomFile(item)
  } else if (filePath.includes('/product')) {
    // 产品信息
    handleProductFile(item)
  } else if (filePath.includes('/manual')) {
    // PDF 手册
    handleManualFile(item)
  } else if (filePath.includes('.mp4')) {
    // 视频文件
    handleVideoFile(item)
  } else {
    // 其他文件
    handleOtherFile(item)
  }
}

const handleBomFile = (item) => {
  currentContentType.value = 'bom'

  // 转换PDF路径为PNG路径
  let imagePath = item.filePath

  // 如果路径不包含 /boms/，则需要转换
  if (!imagePath.includes('/boms/')) {
    let pathParts = imagePath.split('/')
    // 移除空的部分，但保留开头的斜杠信息
    const hasLeadingSlash = imagePath.startsWith('/')
    pathParts = pathParts.filter(part => part.trim() !== '')

    // 在文件名前插入 'boms'
    pathParts.splice(pathParts.length - 1, 0, 'boms')

    // 重新组装路径，确保保留开头的斜杠
    imagePath = (hasLeadingSlash ? '/' : '') + pathParts.join('/').replace('.pdf', '.png')
  }
  // 设置BOM Canvas参数
  Object.assign(bomCanvasParams, {
    filePath: imagePath,
    bomId: item.id,
    materielCode: item.materielCode || '',
    materielName: item.materielName || '',
    materielNameEn: item.materielNameEn || item.fileName?.replace('.pdf', '') || ''
  })
}

const handleProductFile = async (item) => {
  try {
    currentContentType.value = 'product'
    productLoading.value = true
    productError.value = ''

    // 获取产品详细数据
    const productData = await apiService.catalog.getProductCatalog({
      id: item.id
    })

    // 参考PC端逻辑：过滤出产品相关文件
    const products = productData.filter((fileItem) => fileItem.filePath.includes("product"))

    // 构建轮播图列表 - 参考PC端carousel_list逻辑
    const carousel_list = products
      .filter((fileItem) => fileItem.filePath.includes("/product/product"))
      .sort((a, b) => a.filePath.localeCompare(b.filePath))

    // 获取参数图片 - 参考PC端product_params_img逻辑
    const product_params_img = products.find((fileItem) =>
      fileItem.filePath.includes("parameter")
    )

    // 检查购物车和收藏夹状态 - 参考PC端逻辑
    try {
      const currentUserShopCar = await apiService.cart.listDataCurrentUserShopCar()

      // 检查购物车状态 (type: "1")
      const existsInCart = currentUserShopCar.some(cart =>
        cart.type === "1" && cart.bomItemsId === item.id
      )

      // 检查收藏夹状态 (type: "2")
      const existsInFavorites = currentUserShopCar.some(cart =>
        cart.type === "2" && cart.bomItemsId === item.id
      )

      // 构建符合ProductInfo组件要求的数据结构
      currentProduct.value = {
        // 基本产品信息 - 参考PC端tab_list.product[0]结构
        id: item.id,
        tittle: item.tittle || item.materielNameEn || item.materielName || item.fileName,
        materielCode: item.materielCode || '',
        materielName: item.materielName || '',
        materielNameEn: item.materielNameEn || '',
        price: item.price || 0,

        // 状态信息
        existsCart: existsInCart ? '1' : '0',
        existsFavorites: existsInFavorites ? '1' : '0',

        // 图片信息 - 参考PC端carousel_list
        carousel_list: carousel_list,
        product_params_img: product_params_img,

        // 文件列表 - 用于图片显示
        productFileList: products,

        // 原始菜单项数据
        ...item
      }
    } catch (cartError) {
      // 如果获取购物车状态失败，使用默认值
      currentProduct.value = {
        id: item.id,
        tittle: item.tittle || item.materielNameEn || item.materielName || item.fileName,
        materielCode: item.materielCode || '',
        materielName: item.materielName || '',
        materielNameEn: item.materielNameEn || '',
        price: item.price || 0,
        existsCart: '0',
        existsFavorites: '0',
        carousel_list: carousel_list,
        product_params_img: product_params_img,
        productFileList: products,
        ...item
      }
    }

  } catch (err) {
    productError.value = t('detail.loadProductFailed')
    showNotify({ type: 'danger', message: t('detail.loadProductFailed') })
    currentProduct.value = null
  } finally {
    productLoading.value = false
  }
}

const retryLoadProduct = () => {
  // 从当前产品数据中获取原始菜单项信息进行重试
  if (currentProduct.value && currentProduct.value.id) {
    const originalItem = {
      id: currentProduct.value.id,
      tittle: currentProduct.value.tittle,
      materielCode: currentProduct.value.materielCode,
      materielName: currentProduct.value.materielName,
      materielNameEn: currentProduct.value.materielNameEn,
      fileName: currentProduct.value.fileName,
      filePath: currentProduct.value.filePath
    }
    handleProductFile(originalItem)
  } else {
    // 如果没有当前产品数据，显示错误
    productError.value = t('detail.loadProductFailed')
  }
}

const handleManualFile = (item) => {
  currentContentType.value = 'file'
  currentFilePath.value = item.filePath
  currentFileType.value = 'pdf'
}

const handleVideoFile = (item) => {
  currentContentType.value = 'file'
  currentFilePath.value = item.filePath
  currentFileType.value = 'video'
}

const handleOtherFile = (item) => {
  currentContentType.value = 'file'
  currentFilePath.value = item.filePath
  currentFileType.value = 'other'
}

const handleAddToCart = async (product) => {
  try {
    // 参考PC端addWholeCart方法的参数结构
    const result = await apiService.cart.addToCart([{
      amount: product.amount || 1,
      bomItemsId: product.bomItemsId || product.id,
      productType: product.productType || 'product'
    }])

    if (result.code == '0') {
      showNotify({ type: 'success', message: t('detail.addToCartSuccess') })
      // 更新当前产品状态
      if (currentProduct.value && currentProduct.value.id === product.id) {
        currentProduct.value.existsCart = '1'
      }
    } else if (result.code == '1') {
      showNotify({ type: 'success', message: t('detail.removeFromCartSuccess') })
      // 更新当前产品状态
      if (currentProduct.value && currentProduct.value.id === product.id) {
        currentProduct.value.existsCart = '0'
      }
    } else {
      showNotify({ type: 'danger', message: result.msg || t('detail.cartOperationFailed') })
    }
  } catch (error) {
    showNotify({ type: 'danger', message: t('detail.cartOperationFailed') })
  }
}

const handleAddToFavorites = async (product) => {
  try {
    // 参考PC端addWholeFavorit方法的参数结构
    const result = await apiService.cart.addToFavorites([{
      bomItemsId: product.bomItemsId || product.id,
      productType: product.productType || 'product'
    }])

    if (result.code == '0') {
      showNotify({ type: 'success', message: t('detail.addToFavoritesSuccess') })
      // 更新当前产品状态
      if (currentProduct.value && currentProduct.value.id === product.id) {
        currentProduct.value.existsFavorites = '1'
      }
    } else if (result.code == '1') {
      showNotify({ type: 'success', message: t('detail.removeFromFavoritesSuccess') })
      // 更新当前产品状态
      if (currentProduct.value && currentProduct.value.id === product.id) {
        currentProduct.value.existsFavorites = '0'
      }
    } else {
      showNotify({ type: 'danger', message: result.msg || t('detail.favoritesOperationFailed') })
    }
  } catch (error) {
    showNotify({ type: 'danger', message: t('detail.favoritesOperationFailed') })
  }
}

const handlePdfExport = () => {
  // 参考PC端实现：直接打开原始PDF文件
  if (currentContentType.value === 'bom' && bomCanvasParams.filePath) {
    try {
      // 获取原始PDF文件路径 - 从PNG路径转换回PDF路径
      let pdfFilePath = bomCanvasParams.filePath

      // 如果是PNG路径（包含/boms/），需要转换回原始PDF路径
      if (pdfFilePath.includes('/boms/')) {
        // 移除/boms/路径段并将.png改为.pdf
        pdfFilePath = pdfFilePath.replace('/boms/', '/').replace('.png', '.pdf')
      }

      // 构建完整的PDF文件URL - 参考PC端的base_downFileByPath_url + current.value.filePath方式
      const pdfUrl = base_downFileByPath_url + pdfFilePath

      // 使用encodeURIComponent处理空格等特殊字符 - 参考PC端实现
      const encodedUrl = encodeURIComponent(pdfUrl.startsWith('/') ? pdfUrl.substring(1) : pdfUrl)
      window.open(encodedUrl, '_blank')

      showNotify({ type: 'success', message: t('detail.pdfOpened') })
    } catch (error) {
      showNotify({ type: 'danger', message: t('detail.openPdfFailed') })
    }
  } else {
    showNotify({ type: 'warning', message: t('detail.noPdfAvailable') })
  }
}

const handleXlsxExport = () => {
  if (bomCanvasParams.bomId) {
    try {
      // 使用现有的Excel导出URL
      const xlsxExportUrl = `${apiService.urls.bomItemsExcel}${bomCanvasParams.bomId}`
      window.open(xlsxExportUrl, '_blank')
      showNotify({ type: 'success', message: t('detail.downloadStarted') })
    } catch (error) {
      showNotify({ type: 'danger', message: t('detail.downloadFailed') })
    }
  } else {
    showNotify({ type: 'warning', message: t('detail.noBomIdForExcel') })
  }
}

const removeBomsItems = (items) => {
  return items
    .filter((item) => !["boms"].includes(item.fileName))
    .map((item) => ({
      ...item,
      children: item.children ? removeBomsItems(item.children) : undefined,
    }));
};

function filterFiles(files, extensions) {
  return files.filter((item) => {
    if (
      item.fileName &&
      extensions.some((ext) => item.fileName.endsWith(ext))
    ) {
      return false;
    }

    if (item.children && item.children.length > 0) {
      item.children = filterFiles(item.children, extensions);
    }

    return true;
  });
}

function removeChildren(arr, fileName) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].fileName === fileName) {
      delete arr[i].children;
    }
    if (arr[i].children) {
      removeChildren(arr[i].children, fileName);
    }
  }
}
// 获取菜单数据
const getMenuData = async () => {
  try {
    loading.value = true
    error.value = ''
    const result = await apiService.system.getMenu()

    menuData.value = removeBomsItems(result)
    filterFiles(menuData.value, [".txt", ".xlsx", ".xls"]);
    removeChildren(menuData.value, "product");
  } catch (err) {
    error.value = t('detail.loadMenuFailed')
    showNotify({ type: 'danger', message: t('detail.loadMenuFailed') })
  } finally {
    loading.value = false
  }
}



// 重试加载
const retryLoad = () => {
  error.value = ''
  getMenuData()
}

// 根据params参数从菜单数据中查找匹配的菜单项id
const findMenuItemIdByPath = (filePath) => {
  if (!filePath || !menuData.value) return null
  if (filePath.includes('/boms')) {
    filePath = filePath.replace('/boms', '').replace('.png', '.pdf')
  }
  // 递归搜索菜单数据
  const searchInMenu = (items) => {
    for (const item of items) {
      // 检查当前项的filePath是否匹配
      if (item.filePath === filePath) {
        return item.id
      }

      // 如果有子项，递归搜索
      if (item.children && item.children.length > 0) {
        const found = searchInMenu(item.children)
        if (found) return found
      }
    }
    return null
  }

  return searchInMenu(menuData.value)
}

// 处理路由参数
const processRouteParams = () => {
  const { params, filePath, materielCode, materielName, materielNameEn } = route.query

  if (params || filePath) {
    let filePathToUse = params || filePath

    // URL解码params值（如果已编码）
    try {
      // 检查是否为URL编码的字符串
      if (filePathToUse && filePathToUse.includes('%')) {
        filePathToUse = decodeURIComponent(filePathToUse)
      }
    } catch (error) {
      // 如果解码失败，使用原始值
    }

    // 根据params参数从菜单数据中查找匹配的菜单项id
    const menuItemId = findMenuItemIdByPath(filePathToUse)

    // 通过props设置菜单自动展开（无需等待组件创建）
    menuExpandPath.value = filePathToUse
    menuHighlightId.value = menuItemId || ''

    // 根据路由参数结构判断页面类型
    if (isProductDetailPath(filePathToUse)) {
      // 产品详情页面：路径以 /product 结尾
      handleProductDetailFromRoute({
        filePath: filePathToUse,
        id: menuItemId,
        materielCode,
        materielName,
        materielNameEn
      })
    } else if (isBomChartPath(filePathToUse)) {
      // BOM图表页面：路径包含 /boms/ 路径段
      handleBomFile({
        filePath: filePathToUse,
        id: menuItemId, // 使用菜单项id而不是bomId
        materielCode,
        materielName,
        materielNameEn
      })
    } else {
      // 默认处理为BOM文件（保持向后兼容）
      handleBomFile({
        filePath: filePathToUse,
        id: menuItemId, // 使用菜单项id而不是bomId
        materielCode,
        materielName,
        materielNameEn
      })
    }
  } else {
    // 清空菜单展开状态
    menuExpandPath.value = ''
    menuHighlightId.value = ''
  }
}

// 判断是否为产品详情页面路径
const isProductDetailPath = (path) => {
  if (!path) return false
  // 检查路径是否以 /product 结尾
  return path.endsWith('/product')
}

// 判断是否为BOM图表页面路径
const isBomChartPath = (path) => {
  if (!path) return false

  // 检查路径是否包含 /boms/ 路径段
  if (path.includes('/boms/')) {
    return true
  }

  // 检查文件名是否为BOM相关文件（PDF或PNG格式）
  const fileName = path.split('/').pop()?.toLowerCase() || ''
  const isBomFile = fileName.includes('bom') ||
    fileName.includes('diagram') ||
    fileName.includes('assembly') ||
    fileName.includes('parts') ||
    fileName.includes('component')

  // 如果是BOM相关文件且为PDF或PNG格式，则认为是BOM图表
  const isSupportedFormat = fileName.endsWith('.pdf') || fileName.endsWith('.png')

  return isBomFile && isSupportedFormat
}

// 处理来自路由的产品详情请求
const handleProductDetailFromRoute = async (routeData) => {
  try {
    // 从路由参数构建产品项数据
    const productItem = {
      id: routeData.id,
      filePath: routeData.filePath,
      materielCode: routeData.materielCode || '',
      materielName: routeData.materielName || '',
      materielNameEn: routeData.materielNameEn || '',
      // 从路由查询参数中获取其他产品信息
      price: route.query.price || 0,
      amount: route.query.amount || 1,
      existsCart: route.query.existsCart || '0',
      existsFavorites: route.query.existsFavorites || '0',
      model: route.query.model || '',
      serviceCode: route.query.serviceCode || ''
    }

    // 调用产品文件处理方法
    await handleProductFile(productItem)
  } catch (error) {
    showNotify({ type: 'danger', message: t('detail.loadProductFailed') })
  }
}

// 生命周期
onMounted(async () => {
  await getMenuData()
  // 等待下一个tick确保MobileMenu组件已经渲染完成
  await nextTick()
  processRouteParams()
})

// 监听路由变化
watch(() => route.query, () => {
  // 确保菜单数据已加载
  if (menuData.value.length > 0) {
    processRouteParams()
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.detail-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  overflow: hidden;
  /* 为固定导航栏留出空间 - 与购物车页面保持一致 */
  padding-top: $header-height;
}

/* 导航栏样式 - 与其他页面保持一致 */

.menu-icon {
  padding: 8px;
  cursor: pointer;
  transition: opacity 0.2s;
  border-radius: 4px;
}

.menu-icon:active {
  opacity: 0.6;
}

.loading-container,
.error-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
}

.detail-content {
  flex: 1;
  overflow: hidden;
}

.bom-section,
.product-section,
.file-section {
  height: 100%;
}

.empty-section {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid #eee;
  background: white;
  min-height: 40px;
}

.menu-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.bottom-actions {
  position: fixed;
  bottom: $tabbar-height; /* 为底部导航栏留出空间 - 与购物车页面保持一致 */
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  z-index: 100;
}

.bottom-actions .van-button {
  flex: 1;
  min-height: 44px;
  font-weight: 500;
}

/* 响应式设计 - 移动端优先 */

/* 小屏手机适配 (宽度 ≤ 375px) */
@media (max-width: 375px) {
  .detail-nav {
    padding: 0 12px;
  }

  .menu-container {
    width: 100%;
  }

  .bottom-actions {
    padding: 8px 12px;
    gap: 8px;
  }
}

/* 基础横屏模式适配 (所有横屏设备) */
@media (orientation: landscape) {
  .detail-page {
    height: 100vh;
    /* 横屏下减少顶部内边距 */
    padding-top: 36px;
  }

  .detail-nav {
    /* 横屏下导航栏高度优化 */
    height: 36px;

    .van-nav-bar__title {
      font-size: 15px;
      line-height: 1.2;
    }

    .menu-icon {
      padding: 6px;
    }
  }

  .bottom-actions {
    position: relative;
    bottom: auto; /* 横屏模式下重置bottom值 */
    border-top: none;
    padding: 6px 16px;
    background: transparent;

    .van-button {
      min-height: 36px;
      font-size: 13px;
    }
  }
}

/* 大屏手机横屏模式 (宽度 > 567px) */
@media (orientation: landscape) and (min-width: 567px) {
  .detail-page {
    /* 进一步减少顶部内边距 */
    padding-top: 32px;
  }

  .detail-nav {
    /* 大屏横屏下导航栏进一步优化 */
    height: 32px;

    .van-nav-bar__title {
      font-size: 14px;
    }

    .menu-icon {
      padding: 4px;
    }
  }

  .bottom-actions {
    padding: 4px 20px;
    gap: 16px;

    .van-button {
      min-height: 32px;
      font-size: 12px;
      padding: 0 16px;
    }
  }

  .menu-header {
    padding: 8px 12px;
    min-height: 36px;

    .menu-title {
      font-size: 13px;
    }
  }
}

/* 平板设备适配 (宽度 768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .detail-page {
    /* 平板设备下进一步优化布局 */
    padding-top: 40px;
    max-width: 1024px;
    margin: 0 auto;
  }

  .detail-nav {
    height: 40px;
    max-width: 1024px;
    margin: 0 auto;

    .van-nav-bar__title {
      font-size: 16px;
    }

    .menu-icon {
      padding: 8px;
    }
  }

  .bottom-actions {
    max-width: 1024px;
    margin: 0 auto;
    padding: 8px 24px;
    gap: 20px;
    display: flex;
    justify-content: center;
    bottom: 55px;


    .van-button {
      min-height: 40px;
      font-size: 14px;
      padding: 0 20px;
      max-width: 200px;
    }
  }

  .menu-container {
    width: 100%; /* 平板下固定菜单宽度 */
  }

  .menu-header {
    padding: 12px 16px;
    min-height: 48px;

    .menu-title {
      font-size: 15px;
    }
  }
}

/* 触摸优化和交互反馈 */
.van-button {
  -webkit-tap-highlight-color: transparent;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.menu-header .van-icon {
  padding: 6px;
  margin: -6px;
  border-radius: 4px;
  transition: all 0.2s;
  color: #646566;
  min-width: 32px; /* 确保最小触摸目标 */
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-header .van-icon:active {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

/* 确保所有交互元素满足最小触摸目标 */
.menu-icon {
  min-width: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 超宽屏幕优化 (宽度 > 1400px) */
@media (min-width: 1400px) {
  .detail-page {
    max-width: 1400px;
  }

  .detail-nav {
    max-width: 1400px;
  }

  .bottom-actions {
    max-width: 1400px;
    justify-content: center; /* 超宽屏下居中显示按钮 */

    .van-button {
      max-width: 300px;
      margin: 0 12px;
    }
  }

  .menu-container {
    width: 100%; /* 超宽屏下进一步增加菜单宽度 */
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .detail-nav {
    border-bottom: 0.5px solid #eee;
  }

  .bottom-actions {
    border-top: 0.5px solid #eee;
  }

  .menu-header {
    border-bottom: 0.5px solid #eee;
  }
}

/* 深色模式适配 (如果需要) */
@media (prefers-color-scheme: dark) {
  .detail-page {
    background-color: #1a1a1a;
  }

  .menu-header {
    background: #2a2a2a;
    border-bottom-color: #333;

    .menu-title {
      color: #fff;
    }

    .van-icon {
      color: #ccc;
    }
  }

  .bottom-actions {
    background: #2a2a2a;
    border-top-color: #333;
  }
}
</style>
