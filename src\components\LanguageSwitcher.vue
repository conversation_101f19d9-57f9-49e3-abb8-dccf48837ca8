<template>
  <div class="language-switcher">
    <div class="language-display" @click="showPopup = true">
      <span>{{ currentLangText }}</span>
      <van-icon name="arrow-down" class="arrow-icon" />
    </div>
    
    <van-popup
      v-model:show="showPopup"
      position="bottom"
      round
      closeable
      :style="{ height: 'auto', padding: '16px 0' }"
    >
      <div class="popup-title">{{ $t('user.language') }}</div>
      <van-cell-group>
        <van-cell
          v-for="option in options"
          :key="option.value"
          :title="option.text"
          clickable
          @click="selectLanguage(option.value)"
          :class="{ active: currentLang === option.value }"
        >
          <template #right-icon>
            <van-icon v-if="currentLang === option.value" name="success" class="check-icon" />
          </template>
        </van-cell>
      </van-cell-group>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { locale, t } = useI18n();

// 当前语言
const currentLang = ref(localStorage.getItem('locale') || 'zh-CN');
const showPopup = ref(false);

// 语言选项
const options = [
  { text: '中文', value: 'zh-CN' },
  { text: 'English', value: 'en' }
];

// 当前语言文本
const currentLangText = computed(() => {
  const option = options.find(opt => opt.value === currentLang.value);
  return option ? option.text : options[0].text;
});

// 选择语言
const selectLanguage = (value) => {
  locale.value = value;
  localStorage.setItem('locale', value);
  currentLang.value = value;
  showPopup.value = false;
};

// 监听语言变化
watch(
  () => locale.value,
  (newLocale) => {
    currentLang.value = newLocale;
  }
);
</script>

<style lang="scss" scoped>
.language-switcher {
  display: flex;
  align-items: center;
  
  .language-display {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: inherit;
    cursor: pointer;
    
    .arrow-icon {
      margin-left: 4px;
      font-size: 12px;
      color: #969799;
      transition: transform 0.3s;
    }
    
    &:active .arrow-icon {
      transform: rotate(180deg);
    }
  }
}

.popup-title {
  text-align: center;
  padding: 16px;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #ebedf0;
}

:deep(.van-cell) {
  &.active {
    color: #e74c3c;
    font-weight: 500;
  }
  
  .check-icon {
    color: #e74c3c;
  }
}
</style> 