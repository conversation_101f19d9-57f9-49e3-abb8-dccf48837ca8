<template>
  <div class="equipment-list">
    <!-- 加载状态 -->
    <van-loading v-if="loading && data.length === 0" class="loading-center">
      {{ $t("home.loading") }}
    </van-loading>

    <!-- 设备列表 -->
    <div v-else class="equipment-items">
      <div v-for="(item, index) in data" :key="item.id || index" class="equipment-item">
        <!-- 设备信息 - 紧凑布局 -->
        <div class="equipment-compact">
          <!-- 设备图片 -->
          <div class="equipment-image">
            <van-image :src="getEquipmentImage(item.productFileList)" fit="cover"
              @click="previewImages(item.productFileList)">
              <template #error>
                <div class="image-error">
                  <van-icon name="photo-fail" size="16" />
                </div>
              </template>
            </van-image>
          </div>

          <!-- 设备信息区域 -->
          <div class="equipment-info-area">
            <!-- 标题和操作按钮 -->
            <div class="equipment-title-row">
              <h3 class="equipment-model" @click="$emit('viewDetails', item)">
                {{ item.model }}
              </h3>
              <div class="equipment-actions">
                <van-button plain size="small" :loading="item.favoritLoad" @click="$emit('addToFavorites', item)"
                  class="action-btn favorite-btn">
                  <van-icon :name="item.existsFavorites === '1' ? 'star' : 'star-o'" :color="item.existsFavorites === '1' ? '#f39c12' : '#969799'
                    " size="14" />
                </van-button>

                <van-button plain size="small" @click="$emit('viewDetails', item)" class="action-btn detail-btn">
                  <van-icon name="eye-o" color="#969799" size="14" />
                </van-button>
              </div>
            </div>

            <!-- 设备描述 -->
            <div v-if="item.materielSpecification" class="equipment-description">
              <span class="description-text">{{ item.materielSpecification }}</span>
            </div>

            <!-- 服务代码和库存信息 -->
            <div class="equipment-details">
              <div class="service-code">
                <span class="label">{{ $t('home.serviceCode') }}:</span>
                <span class="value">{{ item.serviceCode }}</span>
              </div>

              <!-- 库存信息（仅管理员可见） -->
              <div v-if="userInfo.userType == 1 && item.warehouseAmount" class="stock-info">
                <span class="stock-label">{{ $t('home.stock') }}:</span>
                <span class="stock-value">{{ formatStock(item.warehouseAmount) }}</span>
                <span v-if="item.shelfCode" class="shelf-code">({{ item.shelfCode }})</span>
              </div>
            </div>

            <!-- 价格和购物车 -->
            <div class="equipment-price-row">
              <div class="price-display">
                <span class="currency">{{ getCurrencySymbol() }}</span>
                <span class="price">{{
                  formatPrice(item.totalPrice || item.price)
                  }}</span>
              </div>

              <div class="cart-actions">
                <van-stepper v-model="item.amount" :min="1" @change="onAmountChange(item)" class="quantity-stepper"
                  size="small" />
                <van-button :type="item.existsCart === '1' ? 'warning' : 'primary'" size="small" :loading="item.buyLoad"
                  @click="$emit('addToCart', item)" :icon="item.existsCart === '1'
                      ? 'shopping-cart'
                      : 'shopping-cart-o'
                    " class="cart-btn" />
              </div>
            </div>
          </div>
        </div>

        <!-- 相关图片预览 -->
        <div v-if="hasExpandableImages(item)" class="related-images">
          <!-- 初始显示2-3张图片 -->
          <div class="images-preview">
            <div v-for="(file, fileIndex) in getPreviewImages(item)" :key="fileIndex" class="image-item"
              @click="handleImageClick(item, file, fileIndex)">
              <van-image :src="getImageUrl(file)" fit="cover">
                <template #error>
                  <div class="image-error">
                    <van-icon name="photo-fail" size="16" />
                  </div>
                </template>
              </van-image>
            </div>

            <!-- 显示更多图片按钮 -->
            <div v-if="!item.showImages && getTotalImageCount(item) > 3" class="more-images-btn"
              @click="toggleImages(item)">
              <van-icon name="plus" size="16" />
              <span>+{{ getTotalImageCount(item) - 3 }}</span>
            </div>
          </div>

          <!-- 展开后显示所有图片 -->
          <div v-show="item.showImages" class="images-grid">
            <div class="images-header">
              <span class="images-title">{{ $t("home.allImages") }}</span>
              <van-button plain size="mini" @click="item.showImages = false" class="collapse-btn">
                {{ $t("home.collapse") }}
              </van-button>
            </div>

            <!-- BOM文件图片 -->
            <div v-for="(imgPath, imgIndex) in item.bomsFileList || []" :key="`bom-${imgIndex}`" class="image-item"
              @click="handleBomImageClick(item, imgPath)">
              <van-image :src="getImageUrl(imgPath)" fit="cover" class="grid-image">
                <template #error>
                  <div class="image-error-small">
                    <van-icon name="photo-fail" size="16" />
                  </div>
                </template>
              </van-image>
              <div class="image-name">{{ getImageName(imgPath) }}</div>
            </div>

            <!-- 引擎文件图片 -->
            <div v-for="(imgPath, imgIndex) in item.engineFileList || []" :key="`engine-${imgIndex}`" class="image-item"
              @click="previewSingleImage(imgPath)">
              <van-image :src="getImageUrl(imgPath)" fit="cover" class="grid-image">
                <template #error>
                  <div class="image-error-small">
                    <van-icon name="photo-fail" size="16" />
                  </div>
                </template>
              </van-image>
              <div class="image-name">{{ getImageName(imgPath) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无数据提示 -->
    <van-empty v-if="!loading && data.length === 0" :description="$t('home.noEquipmentData')" class="empty-state" />
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { showImagePreview } from "vant";
import { useUserStore } from "@/stores/user.js";
import apiService from "@/utils/api";

const { t } = useI18n();
const userStore = useUserStore();

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  hasMore: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "addToCart",
  "addToFavorites",
  "viewDetails",
  "viewBom",
  "navigateToDetail",
]);

// 用户信息
const userInfo = computed(() => {
  return userStore.userInfo || {};
});

// 获取设备主图
const getEquipmentImage = (fileList) => {
  if (!fileList || fileList.length === 0) return "";

  const productImages = fileList.filter((path) => {
    const fileName = path.split("/").pop();
    return (
      fileName.includes("product") &&
      (fileName.includes(".jpg") || fileName.includes(".png"))
    );
  });

  return productImages.length > 0 ? `/fileviewer${productImages[0]}` : "";
};

// 获取图片URL
const getImageUrl = (path) => {
  return `/fileviewer${path}`;
};

// 获取图片名称
const getImageName = (path) => {
  const fileName = path.split("/").pop();
  return fileName.replace(/\.[^/.]+$/, "");
};

// 检查是否有可展开的图片
const hasExpandableImages = (item) => {
  return (
    (item.bomsFileList && item.bomsFileList.length > 0) ||
    (item.engineFileList && item.engineFileList.length > 0)
  );
};

// 切换图片显示
const toggleImages = (item) => {
  item.showImages = !item.showImages;
};

// 预览图片
const previewImages = (fileList) => {
  if (!fileList || fileList.length === 0) return;

  const images = fileList
    .filter((path) => {
      const fileName = path.split("/").pop();
      return fileName.includes(".jpg") || fileName.includes(".png");
    })
    .map((path) => `/fileviewer${path}`);

  if (images.length > 0) {
    showImagePreview(images);
  }
};

// 预览单张图片
const previewSingleImage = (path) => {
  showImagePreview([`/fileviewer${path}`]);
};

// 数量变化处理
const onAmountChange = (item) => {
  item.totalPrice = parseFloat(
    (parseFloat(item.price || 0) * parseFloat(item.amount || 1)).toFixed(2)
  );
};

// 格式化价格
const formatPrice = (value) => {
  return apiService.utils.formatPrice(value);
};

// 格式化库存
const formatStock = (value) => {
  return apiService.utils.formatStock(value);
};

// 获取货币符号
const getCurrencySymbol = () => {
  const currency = userInfo.value.currency;
  switch (currency) {
    case "1":
      return "¥";
    case "2":
      return "$";
    case "3":
      return "€";
    default:
      return "";
  }
};

// 获取预览图片（前3张）
const getPreviewImages = (item) => {
  const allImages = [];

  // 添加BOM图片
  if (item.bomsFileList && item.bomsFileList.length > 0) {
    allImages.push(...item.bomsFileList);
  }

  // 添加引擎图片
  if (item.engineFileList && item.engineFileList.length > 0) {
    allImages.push(...item.engineFileList);
  }

  return allImages.slice(0, 3);
};

// 获取总图片数量
const getTotalImageCount = (item) => {
  let count = 0;
  if (item.bomsFileList) count += item.bomsFileList.length;
  if (item.engineFileList) count += item.engineFileList.length;
  return count;
};

// 预览所有图片
const previewAllImages = (item, startIndex = 0) => {
  const allImages = [];

  // 添加BOM图片
  if (item.bomsFileList && item.bomsFileList.length > 0) {
    allImages.push(...item.bomsFileList.map((path) => `/fileviewer${path}`));
  }

  // 添加引擎图片
  if (item.engineFileList && item.engineFileList.length > 0) {
    allImages.push(...item.engineFileList.map((path) => `/fileviewer${path}`));
  }

  if (allImages.length > 0) {
    showImagePreview({
      images: allImages,
      startPosition: startIndex,
    });
  }
};

// 处理images-preview区域的图片点击
const handleImageClick = (item, filePath, fileIndex) => {
  // 判断是否为BOM文件
  if (isBomFile(filePath)) {
    // BOM文件跳转到详情页
    handleBomImageClick(item, filePath);
  } else {
    // 其他图片使用预览功能
    previewAllImages(item, fileIndex);
  }
};

// 处理BOM文件图片点击 - 跳转到详情页
const handleBomImageClick = (item, filePath) => {
  // 构建BOM图表页面的路由参数
  const bomParams = constructBomParams(item, filePath);

  // 触发导航事件
  emit('navigateToDetail', bomParams);
};

// 判断是否为BOM文件
const isBomFile = (filePath) => {
  if (!filePath) return false;

  // 检查文件路径是否包含BOM相关标识
  const fileName = filePath.toLowerCase();
  return fileName.includes('bom') ||
    fileName.includes('diagram') ||
    fileName.includes('assembly') ||
    fileName.includes('parts');
};

// 构建BOM参数
const constructBomParams = (item, filePath) => {
  // 直接使用原始文件路径，让详情页的handleBomFile函数处理/boms/路径段的添加
  // 这样避免重复添加/boms/路径段
  const bomPath = filePath;

  return {
    path: '/detail',
    query: {
      params: encodeURIComponent(bomPath), // URL编码params值
      bomId: item.id,
      materielCode: item.materielCode || '',
      materielName: item.materielName || '',
      materielNameEn: item.materielNameEn || item.model || '',
      // 传递设备的基本信息
      equipmentId: item.id,
      model: item.model,
      serviceCode: item.serviceCode,
      price: item.price || item.totalPrice,
      amount: item.amount,
      existsCart: item.existsCart,
      existsFavorites: item.existsFavorites
    }
  };
};
</script>

<style lang="scss" scoped>
.equipment-list {
  padding: 0;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.equipment-items {
  padding: 0;
}

.equipment-item {
  background: white;
  margin-bottom: 6px;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:last-child {
    margin-bottom: 0;
  }
}

.equipment-compact {
  display: flex;
  gap: 8px;

  .equipment-image {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;

    .van-image {
      width: 100%;
      height: 100%;
    }

    .image-error {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      background: #f5f5f5;
      color: #969799;
    }
  }

  .equipment-info-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .equipment-title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .equipment-model {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #323233;
      cursor: pointer;
      flex: 1;
      line-height: 1.2;

      &:hover {
        color: #1989fa;
      }
    }

    .equipment-actions {
      display: flex;
      gap: 4px;
      margin-left: 8px;

      .action-btn {
        min-width: 24px;
        height: 24px;
        padding: 0;
        border: none;
        background: transparent;

        .van-icon {
          font-size: 14px;
        }
      }
    }
  }

  .equipment-description {
    margin: 4px 0;

    .description-text {
      font-size: 12px;
      color: #646566;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .equipment-details {
    margin: 4px 0;
    font-size: 11px;

    .service-code {
      margin-bottom: 2px;

      .label {
        color: #969799;
      }

      .value {
        color: #323233;
        font-weight: 500;
        margin-left: 4px;
      }
    }

    .stock-info {
      color: #e74c3c;

      .stock-label {
        font-weight: 500;
      }

      .stock-value {
        font-weight: 600;
        margin: 0 2px;
      }

      .shelf-code {
        color: #969799;
        margin-left: 4px;
      }
    }
  }

  .equipment-price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .price-display {
      .currency {
        font-size: 20px;
        color: #e74c3c;
        margin-right: 3px;
        font-weight: 600;
      }

      .price {
        font-size: 20px;
        font-weight: 700;
        color: #e74c3c;
      }
    }

    .cart-actions {
      display: flex;
      align-items: center;
      gap: 6px;

      .quantity-stepper {
        :deep(.van-stepper) {
          .van-stepper__input {
            width: 40px;
            height: 24px;
            font-size: 12px;
          }

          .van-stepper__minus,
          .van-stepper__plus {
            width: 24px;
            height: 24px;
          }
        }
      }

      .cart-btn {
        min-width: 28px;
        height: 24px;
        padding: 0;
      }
    }
  }
}

.expanded-images {
  margin-top: 16px;
  border-top: 1px solid #ebedf0;
  padding-top: 16px;

  .images-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .images-title {
      font-size: 14px;
      font-weight: 500;
      color: #323233;
    }

    .collapse-btn {
      height: 24px;
      padding: 0 8px;
      font-size: 12px;
    }
  }

  .images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;

    .image-item {
      cursor: pointer;

      .grid-image {
        width: 100%;
        height: 60px;
        border-radius: 4px;
        overflow: hidden;
      }

      .image-name {
        font-size: 10px;
        color: #646566;
        text-align: center;
        margin-top: 4px;
        word-break: break-all;
        line-height: 1.2;
      }

      .image-error-small {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        background: #f5f5f5;
        color: #969799;
      }
    }
  }
}

.expand-section {
  margin-top: 12px;
  text-align: center;

  .expand-btn {
    height: 32px;
    font-size: 13px;
    color: #1989fa;
    border-color: #1989fa;

    .van-icon {
      margin-right: 4px;
    }
  }
}

.related-images {
  margin-top: 12px;

  .images-preview {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .image-item {
      width: 60px;
      height: 60px;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;

      .van-image {
        width: 100%;
        height: 100%;
      }

      .image-error {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        background: #f5f5f5;
        color: #969799;
      }
    }

    .more-images-btn {
      width: 60px;
      height: 60px;
      border-radius: 4px;
      background: #f7f8fa;
      border: 1px dashed #c8c9cc;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #969799;
      font-size: 12px;

      &:hover {
        background: #f2f3f5;
      }

      .van-icon {
        margin-bottom: 2px;
      }
    }
  }

  .images-grid {
    margin-top: 12px;

    .images-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .images-title {
        font-size: 14px;
        font-weight: 500;
        color: #323233;
      }

      .collapse-btn {
        color: #1989fa;
        border-color: #1989fa;
      }
    }
  }
}

.empty-state {
  padding: 60px 0;
}

// 响应式设计
@media (max-width: 768px) {
  .equipment-info {
    padding: 12px;
  }

  .equipment-header {
    .equipment-model {
      font-size: 15px;
    }
  }

  .equipment-pricing {
    .price-section {
      .price-display {
        .currency {
          font-size: 18px;
        }

        .price {
          font-size: 18px;
        }
      }
    }
  }

  .expanded-images {
    .images-grid {
      grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
      gap: 6px;

      .image-item {
        .grid-image {
          height: 50px;
        }

        .image-name {
          font-size: 9px;
        }
      }
    }
  }
}
</style>
