import { createRouter, createWeb<PERSON>ashHistory } from "vue-router";
import { useUserStore } from "@/stores/user";

// 布局
import DefaultLayout from "@/layouts/DefaultLayout.vue";

const routes = [
  {
    path: "/login",
    name: "login",
    component: () => import("@/pages/login/index.vue"),
    meta: {
      title: "登录",
      requiresAuth: false,
    },
  },
  {
    path: "/",
    redirect: '/home',
    component: DefaultLayout,
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: "/home",
        name: "home",
        component: () => import("@/pages/home/<USER>"),
        meta: {
          title: "首页",
        },
      },
      {
        path: "/catalog",
        name: "catalog",
        component: () => import("@/pages/catalog/index.vue"),
        meta: {
          title: "产品目录",
        },
      },
      {
        path: "/order",
        name: "Order",
        component: () => import("@/pages/order/index.vue"),
        meta: {
          title: "订单管理",
          requiresAuth: true,
          layout: 'default'
        },
      },
      {
        path: "/order/create",
        name: "OrderCreate",
        component: () => import("@/pages/order/create.vue"),
        meta: {
          requiresAuth: true,
          layout: 'default'
        },
      },
      {
        path: "/notice",
        name: "notice",
        component: () => import("@/pages/notice/index.vue"),
        meta: {
          title: "通知中心",
        },
      },
      {
        path: "/user",
        name: "user",
        component: () => import("@/pages/user/index.vue"),
        meta: {
          title: "个人中心",
        },
      },
      {
        path: "/favor",
        name: "favor",
        component: () => import("@/pages/favor/index.vue"),
        meta: {
          title: "我的收藏",
          requiresAuth: true,
        },
      },
      {
        path: "/cart",
        name: "cart",
        component: () => import("@/pages/cart/index.vue"),
        meta: {
          title: "购物车",
          requiresAuth: true,
        },
      },
      {
        path: "/warranty",
        name: "warranty",
        component: () => import("@/pages/warranty/index.vue"),
        meta: {
          title: "质保管理",
          requiresAuth: true,
        },
      },
      {
        path: "/detail",
        name: "detail",
        component: () => import("@/pages/detail/index.vue"),
        meta: {
          title: "产品详情",
          requiresAuth: true,
        },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 路由拦截
router.beforeEach((to, from, next) => {
  // 设置标题
  if (to.meta.title) {
    document.title = to.meta.title;
  }

  // 使用Pinia store检查用户是否已登录
  const userStore = useUserStore();
  
  const isAuthenticated = userStore.isLoggedIn();
  const requiresAuth = to.matched.some((record) => record.meta.requiresAuth);

  if (requiresAuth && !isAuthenticated) {
    // 如果需要认证但未登录，则重定向到登录页
    next({ name: "login" });
  } else if (to.name === "login" && isAuthenticated) {
    // 如果已登录又访问登录页，则重定向到首页
    next({ name: "home" });
  } else {
    next();
  }
});

export default router;
