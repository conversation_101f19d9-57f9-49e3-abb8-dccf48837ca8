/**
 * 全局配置文件
 * 存放API基础路径和其他全局配置
 */

// 环境配置类型定义
interface EnvironmentConfig {
  baseUrl: string;
  imgUrl: string;
  fileUrl: string;
}

// 配置对象类型定义
interface Config {
  development: EnvironmentConfig;
  production: EnvironmentConfig;
}

// API基础路径
export const BASE_URL: string = "/sparts-wb";

// 图片基础路径
export const BASE_IMG_URL: string = "/fileviewer/_Files/";

// 文件下载基础路径
export const BASE_FILE_URL: string = "/fileviewer/";

// 其他全局配置
export const config: Config = {
  // 根据环境设置不同的配置
  development: {
    baseUrl: "/sparts-wb",
    imgUrl: "/fileviewer/_Files/",
    fileUrl: "/fileviewer/",
  },
  production: {
    baseUrl: "/sparts-wb",
    imgUrl: "/fileviewer/_Files/",
    fileUrl: "/fileviewer/",
  },
};

// 根据当前环境获取配置
export const currentConfig: EnvironmentConfig = config[(import.meta as any).env?.MODE || "development"];

// 默认导出配置对象，用于与现有配置系统兼容
export default {
  BASE_URL,
  BASE_IMG_URL,
  BASE_FILE_URL,
  config,
  currentConfig
};