<template>
  <div class="catalog-page">
    <!-- 页面头部 -->
    <van-nav-bar
      :title="getCurrentTitle()"
      :left-arrow="currentLevel > 0"
      @click-left="goBack"
      fixed
    />

    <!-- 主要内容区域 -->
    <div class="content-container">
      <!-- 品牌列表 -->
      <div v-if="currentLevel === 0" class="brand-list-container">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <div v-if="brandList.length > 0" class="brand-list">
            <div
              v-for="brand in brandList"
              :key="brand.id"
              class="brand-item"
              @click="selectBrand(brand)"
            >
              <div class="brand-image">
                <van-image
                  :src="getBrandImageUrl(brand.fileName)"
                  fit="contain"
                  :show-error="false"
                  :show-loading="false"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <van-icon name="photo-o" size="32" />
                    </div>
                  </template>
                </van-image>
              </div>
              <div class="brand-info">
                <div class="brand-name">{{ brand.fileName }}</div>
                <van-icon name="arrow" class="brand-arrow" />
              </div>
            </div>
          </div>

          <!-- 品牌列表为空 -->
          <van-empty
            v-else-if="!loading"
            :description="$t('catalog.noBrandsData')"
          />
        </van-pull-refresh>
      </div>

      <!-- 产品列表/详情 -->
      <div v-else class="product-container">
        <!-- 产品导航列表 -->
        <div v-if="currentLevel < 4" class="product-list-container">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <div v-if="productList.length > 0" class="product-grid">
              <div
                v-for="product in productList"
                :key="product.id"
                class="product-item"
                @click="selectProduct(product)"
              >
                <div class="product-image">
                  <van-image
                    :src="getProductImageUrl(product.fileName)"
                    fit="contain"
                    :show-error="false"
                    :show-loading="false"
                  >
                    <template #error>
                      <div class="image-placeholder">
                        <van-icon name="photo-o" size="24" />
                      </div>
                    </template>
                  </van-image>
                </div>
                <div class="product-name">{{ removeFileExtension(product.fileName) }}</div>
              </div>
            </div>

            <!-- 产品列表为空 -->
            <van-empty
              v-else-if="!loading"
              :description="$t('catalog.noProductsData')"
            />
          </van-pull-refresh>
        </div>

        <!-- 产品详情页面 (BOM级别) -->
        <div v-else class="product-detail-container">
          <van-tabs v-model:active="activeTab" @change="onTabChange">
            <!-- 设备信息 -->
            <van-tab :title="$t('catalog.equipmentTab')" name="equipment">
              <div v-if="productDetail" class="equipment-detail">
                <div class="equipment-header">
                  <div class="equipment-info">
                    <h3 class="equipment-title">{{ productDetail.tittle || productDetail.materielName }}</h3>
                    <p class="equipment-code">{{ productDetail.materielCode }}</p>
                    <div class="equipment-price">
                      <span class="currency-symbol">{{ getCurrencySymbol() }}</span>
                      <span class="price-value">{{ formatPrice(productDetail.price) }}</span>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="equipment-actions">
                    <van-button
                      :icon="productDetail.existsFavorites == '1' ? 'star' : 'star-o'"
                      :type="productDetail.existsFavorites == '1' ? 'warning' : 'default'"
                      size="small"
                      @click="toggleFavorite(productDetail)"
                    >
                      {{ productDetail.existsFavorites == '1' ? $t('catalog.removeFromFavorites') : $t('catalog.addToFavorites') }}
                    </van-button>

                    <van-button
                      icon="shopping-cart-o"
                      type="danger"
                      size="small"
                      @click="showQuantityPopup = true"
                    >
                      {{ productDetail.existsCart == '1' ? $t('catalog.removeFromCart') : $t('catalog.addToCart') }}
                    </van-button>
                  </div>
                </div>

                <!-- 产品图片 -->
                <div v-if="productImages.length > 0" class="equipment-images">
                  <van-swipe :autoplay="3000" indicator-color="white">
                    <van-swipe-item v-for="(image, index) in productImages" :key="index">
                      <van-image
                        :src="image"
                        fit="contain"
                        class="equipment-image"
                        @click="previewImages(productImages, index)"
                      />
                    </van-swipe-item>
                  </van-swipe>
                </div>

                <!-- 规格参数图片 -->
                <div v-if="parameterImage" class="parameter-section">
                  <h4>{{ $t('catalog.specifications') }}</h4>
                  <van-image
                    :src="parameterImage"
                    fit="contain"
                    class="parameter-image"
                    @click="previewImages([parameterImage], 0)"
                  />
                </div>
              </div>
            </van-tab>

            <!-- 配件列表 -->
            <van-tab :title="$t('catalog.spareParts')" name="parts">
              <div v-if="bomList.length > 0" class="parts-list">
                <div
                  v-for="part in bomList"
                  :key="part.id"
                  class="part-item"
                  @click="viewBomDiagram(part)"
                >
                  <div class="part-image">
                    <van-image
                      :src="getFileUrl(part.filePath)"
                      fit="contain"
                      :show-error="false"
                      :show-loading="false"
                    >
                      <template #error>
                        <div class="image-placeholder">
                          <van-icon name="photo-o" size="20" />
                        </div>
                      </template>
                    </van-image>
                  </div>
                  <div class="part-info">
                    <div class="part-name">{{ removeFileExtension(part.fileName) }}</div>
                    <van-icon
                      name="eye-o"
                      size="20"
                      class="view-bom-icon"
                      @click.stop="viewBomDiagram(part)"
                    />
                  </div>
                </div>
              </div>
              <van-empty v-else :description="$t('catalog.noProductsData')" />
            </van-tab>

            <!-- 视频 -->
            <van-tab :title="$t('catalog.videoTab')" name="video">
              <div v-if="videoList.length > 0" class="video-list">
                <div
                  v-for="video in videoList"
                  :key="video.id"
                  class="video-item"
                  @click="playVideo(video)"
                >
                  <div class="video-thumbnail">
                    <van-image
                      src="/assets/images/video.jpg"
                      fit="cover"
                      class="video-image"
                    />
                    <div class="play-button">
                      <van-icon name="play" size="24" />
                    </div>
                  </div>
                  <div class="video-name">{{ removeFileExtension(video.fileName) }}</div>
                </div>
              </div>
              <van-empty v-else :description="$t('catalog.noProductsData')" />
            </van-tab>

            <!-- 手册 -->
            <van-tab :title="$t('catalog.manualTab')" name="manual">
              <div v-if="manualList.length > 0" class="manual-list">
                <div
                  v-for="manual in manualList"
                  :key="manual.id"
                  class="manual-item"
                  @click="openManual(manual)"
                >
                  <div class="manual-icon">
                    <van-image
                      src="/assets/images/pdf.jpg"
                      fit="cover"
                      class="manual-image"
                    />
                  </div>
                  <div class="manual-info">
                    <div class="manual-name">{{ removeFileExtension(manual.fileName) }}</div>
                    <van-button
                      size="mini"
                      type="primary"
                      @click.stop="openManual(manual)"
                    >
                      {{ $t('catalog.viewManual') }}
                    </van-button>
                  </div>
                </div>
              </div>
              <van-empty v-else :description="$t('catalog.noProductsData')" />
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-overlay" vertical>
      {{ getLoadingText() }}
    </van-loading>



    <!-- 数量选择弹窗 -->
    <van-popup v-model:show="showQuantityPopup" round closeable>
      <div class="quantity-popup">
        <div class="popup-title">{{ $t('catalog.confirmQuantity') }}</div>
        <div class="quantity-input">
          <van-stepper v-model="selectedQuantity" min="1" max="9999" />
        </div>
        <div class="popup-actions">
          <van-button @click="showQuantityPopup = false">
            {{ $t('catalog.cancel') }}
          </van-button>
          <van-button type="primary" @click="confirmAddToCart">
            {{ $t('catalog.confirm') }}
          </van-button>
        </div>
      </div>
    </van-popup>


  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { showNotify, showImagePreview } from 'vant'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import apiService from '@/utils/api'
import { BASE_IMG_URL, BASE_FILE_URL } from '@/config/config'

const { t } = useI18n()
const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const currentLevel = ref(0) // 0: 品牌, 1: productType, 2: model, 3: bom, 4: 详情
const activeTab = ref('equipment')



// 数量选择
const showQuantityPopup = ref(false)
const selectedQuantity = ref(1)



// 数据列表
const brandList = ref([])
const productList = ref([])
const productDetail = ref(null)
const bomList = ref([])
const videoList = ref([])
const manualList = ref([])
const productImages = ref([])
const parameterImage = ref('')

// 导航相关
const navigationStack = ref([]) // 存储导航历史

// 计算属性
const userInfo = computed(() => userStore.userInfo || {})

// 获取当前页面标题
const getCurrentTitle = () => {
  switch (currentLevel.value) {
    case 0:
      return t('catalog.brandList')
    case 1:
    case 2:
    case 3:
      return t('catalog.productList')
    case 4:
      return t('catalog.productDetails')
    default:
      return t('catalog.title')
  }
}

// 获取加载文本
const getLoadingText = () => {
  return currentLevel.value === 0 ? t('catalog.loadingBrands') : t('catalog.loadingProducts')
}

// 获取品牌图片URL
const getBrandImageUrl = (fileName) => {
  return `${BASE_IMG_URL}${fileName}.png?t=${Date.now()}`
}

// 获取产品图片URL
const getProductImageUrl = (fileName) => {
  return `${BASE_IMG_URL}${fileName}.png`
}

// 获取文件URL
const getFileUrl = (filePath) => {
  return `${BASE_FILE_URL}${filePath}`
}

// 移除文件扩展名
const removeFileExtension = (filename) => {
  if (!filename) return ''
  const lastDotIndex = filename.lastIndexOf('.')
  if (lastDotIndex === -1) return filename
  return filename.substring(0, lastDotIndex)
}

// 获取货币符号
const getCurrencySymbol = () => {
  const currency = userInfo.value.currency
  switch (currency) {
    case '1': return '¥'
    case '2': return '$'
    case '3': return '€'
    default: return '$'
  }
}

// 格式化价格
const formatPrice = (price) => {
  if (!price) return '0.00'
  return parseFloat(price).toFixed(2)
}

// 获取下一级类型
const getNextType = (type) => {
  switch (type) {
    case 'brand':
      return 'productType'
    case 'productType':
      return 'model'
    case 'model':
      return 'bom'
    default:
      return ''
  }
}

// 初始化 - 获取品牌列表
const getBrandList = async () => {
  try {
    loading.value = true
    const response = await apiService.catalog.getBrandList()
    brandList.value = response || []
  } catch (error) {
    showNotify({ type: 'danger', message: t('catalog.loadingFailed') })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 获取产品列表
const getProductList = async (item) => {
  try {
    loading.value = true

    if (item.type === 'bom') {
      // BOM级别 - 获取详细信息
      await getProductDetail(item)
    } else {
      // 其他级别 - 获取子级产品
      const response = await apiService.catalog.getProductCatalog({
        id: item.id,
        type: getNextType(item.type)
      })
      productList.value = response || []
    }
  } catch (error) {
    showNotify({ type: 'danger', message: t('catalog.loadingFailed') })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 获取产品详情
const getProductDetail = async (item) => {
  try {
    loading.value = true
    // 重置标签页到设备标签页
    activeTab.value = 'equipment'

    const response = await apiService.catalog.getProductCatalog({
      id: item.id
    })

    if (response && response.length > 0) {
      // 分类数据
      bomList.value = response.filter(
        item => item.filePath.includes('boms') && item.fileName !== 'boms'
      )

      manualList.value = response.filter(
        item => item.filePath.includes('manual') && !item.filePath.endsWith('manual')
      )

      videoList.value = response.filter(item => item.filePath.includes('mp4'))

      const productData = response.filter(item => item.filePath.endsWith('product'))
      productDetail.value = productData[0] || null

      // 获取产品图片
      productImages.value = response
        .filter(item => item.filePath.includes('/product/product'))
        .sort((a, b) => a.filePath.localeCompare(b.filePath))
        .map(item => getFileUrl(item.filePath))

      // 获取参数图片
      const parameterData = response.filter(item =>
        item.filePath.includes('/product/parameter')
      )
      parameterImage.value = parameterData.length > 0 ? getFileUrl(parameterData[0].filePath) : ''

      // 检查购物车和收藏状态
      if (productDetail.value) {
        await checkCartAndFavoriteStatus()
      }
    }
  } catch (error) {
    console.error('获取产品详情失败:', error)
    showNotify({ type: 'danger', message: t('catalog.loadingFailed') })
  } finally {
    loading.value = false
  }
}

// 检查购物车和收藏状态
const checkCartAndFavoriteStatus = async () => {
  try {
    const response = await apiService.cart.listDataCurrentUserShopCar()
    if (response && productDetail.value) {
      // 检查购物车状态
      if (response.some(cart => cart.type == '1' && cart.bomItemsId === productDetail.value.id)) {
        productDetail.value.existsCart = '1'
      }
      // 检查收藏状态
      if (response.some(cart => cart.type == '2' && cart.bomItemsId === productDetail.value.id)) {
        productDetail.value.existsFavorites = '1'
      }
    }
  } catch (error) {
    // 检查购物车和收藏状态失败
  }
}

// 选择品牌
const selectBrand = (brand) => {
  navigationStack.value.push({
    level: currentLevel.value,
    data: brandList.value
  })

  currentLevel.value = 1
  getProductList(brand)
}

// 选择产品
const selectProduct = (product) => {
  navigationStack.value.push({
    level: currentLevel.value,
    data: productList.value
  })

  if (product.type === 'bom') {
    currentLevel.value = 4
  } else {
    currentLevel.value++
  }

  getProductList(product)
}

// 返回上一级
const goBack = () => {
  if (navigationStack.value.length > 0) {
    const previousState = navigationStack.value.pop()
    currentLevel.value = previousState.level

    if (currentLevel.value === 0) {
      brandList.value = previousState.data
    } else {
      productList.value = previousState.data
    }
  }
}



// 下拉刷新
const onRefresh = () => {
  if (currentLevel.value === 0) {
    getBrandList()
  } else {
    // 刷新当前产品列表
    const lastNavState = navigationStack.value[navigationStack.value.length - 1]
    if (lastNavState) {
      refreshing.value = false
    }
  }
}

// 标签页切换
const onTabChange = (name) => {
  activeTab.value = name
}

// 预览图片
const previewImages = (images, startIndex = 0) => {
  if (images && images.length > 0) {
    showImagePreview({
      images,
      startPosition: startIndex,
      closeable: true
    })
  }
}

// 切换收藏状态
const toggleFavorite = async (product) => {
  try {
    const response = await apiService.cart.addToFavorites([{
      bomItemsId: product.id,
      productType: 'product'
    }])

    if (response.code == '0') {
      product.existsFavorites = '1'
      showNotify({ type: 'success', message: t('catalog.favoriteSuccess') })
    } else if (response.code == '1') {
      product.existsFavorites = '0'
      showNotify({ type: 'success', message: t('catalog.unfavoriteSuccess') })
    } else {
      showNotify({ type: 'danger', message: response.msg || t('catalog.operationFailed') })
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    showNotify({ type: 'danger', message: t('catalog.operationFailed') })
  }
}

// 确认添加到购物车
const confirmAddToCart = async () => {
  try {
    const response = await apiService.cart.addToCart([{
      amount: selectedQuantity.value,
      bomItemsId: productDetail.value.id,
      productType: 'product'
    }])

    if (response.code == '0') {
      productDetail.value.existsCart = '1'
      showNotify({ type: 'success', message: t('catalog.addSuccess') })
    } else if (response.code == '1') {
      productDetail.value.existsCart = '0'
      showNotify({ type: 'success', message: t('catalog.removeSuccess') })
    } else {
      showNotify({ type: 'danger', message: response.msg || t('catalog.operationFailed') })
    }
  } catch (error) {
    console.error('购物车操作失败:', error)
    showNotify({ type: 'danger', message: t('catalog.operationFailed') })
  } finally {
    showQuantityPopup.value = false
    selectedQuantity.value = 1
  }
}

// 跳转到配件详情页面
const viewBomDiagram = (item) => {
  // 构建BOM图表路径 - 确保路径包含 /boms/ 路径段
  let bomPath = item.filePath
  if (!bomPath.includes('/boms/')) {
    // 如果路径不包含 /boms/，需要添加
    const pathParts = bomPath.split('/')
    const fileName = pathParts.pop() // 移除文件名
    bomPath = pathParts.join('/') + '/boms/' + fileName
  }

  // 跳转到详情页面，传递BOM相关参数
  router.push({
    path: '/detail',
    query: {
      params: encodeURIComponent(bomPath), // URL编码params值
      bomId: item.id,
      materielCode: item.materielCode || '',
      materielName: item.materielName || removeFileExtension(item.fileName),
      materielNameEn: item.materielNameEn || '',
      filePath: encodeURIComponent(item.filePath), // URL编码filePath值
      indexNo: 0,
      quantity: 0,
      amount: 0,
      price: 0
    }
  })
}

// 播放视频
const playVideo = (video) => {
  const videoUrl = getFileUrl(video.filePath)
  window.open(videoUrl, '_blank')
}

// 打开手册
const openManual = (manual) => {
  const manualUrl = getFileUrl(manual.filePath)
  window.open(manualUrl, '_blank')
}

// 组件挂载时初始化
onMounted(() => {
  getBrandList()
})
</script>

<style lang="scss" scoped>
.catalog-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  padding-top: 46px; // 为固定导航栏留出空间
}

.content-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

// 品牌列表样式
.brand-list-container {
  height: 100%;
  overflow: hidden;

  .van-pull-refresh {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

.brand-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  min-height: 100%;
}

.brand-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .brand-image {
    width: 80px;
    height: 80px;
    margin-right: 16px;
    flex-shrink: 0;

    .van-image {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }

    .image-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #f5f5f5;
      border-radius: 8px;
      color: #ccc;
    }
  }

  .brand-info {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .brand-name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .brand-arrow {
      font-size: 16px;
      color: #e74c3c;
    }
  }
}

// 产品列表样式
.product-list-container {
  height: 100%;
  overflow: hidden;

  .van-pull-refresh {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
  min-height: 100%;
}

.product-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .product-image {
    width: 100%;
    height: 100px;
    margin-bottom: 12px;

    .van-image {
      width: 100%;
      height: 100%;
    }

    .image-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #f5f5f5;
      border-radius: 8px;
      color: #ccc;
    }
  }

  .product-name {
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
  }
}

// 产品详情样式
.product-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .van-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.van-tabs__wrap) {
      flex-shrink: 0;
    }

    :deep(.van-tabs__content) {
      flex: 1;
      overflow: hidden;

      .van-tab__panel {
        max-height: calc(100vh - 130px );
        overflow-y: auto;
        padding: 16px;
        box-sizing: border-box;
      }
    }
  }
}

.equipment-detail {
  .equipment-header {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .equipment-info {
      margin-bottom: 16px;

      .equipment-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .equipment-code {
        font-size: 14px;
        color: #666;
        margin-bottom: 12px;
      }

      .equipment-price {
        display: flex;
        align-items: baseline;

        .currency-symbol {
          font-size: 16px;
          color: #ff6b35;
          font-weight: 600;
          margin-right: 4px;
        }

        .price-value {
          font-size: 20px;
          color: #ff6b35;
          font-weight: 600;
        }
      }
    }

    .equipment-actions {
      display: flex;
      gap: 12px;

      .van-button {
        flex: 1;
        height: 36px;
      }
    }
  }

  .equipment-images {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .van-swipe {
      border-radius: 8px;
      overflow: hidden;

      .equipment-image {
        width: 100%;
        height: 200px;
        cursor: pointer;
      }
    }
  }

  .parameter-section {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
    }

    .parameter-image {
      width: 100%;
      border-radius: 8px;
      cursor: pointer;
    }
  }
}

.parts-list {
  // padding 由 .van-tab__panel 统一处理

  .part-item {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }

    .part-image {
      width: 80px;
      height: 80px;
      margin-right: 16px;
      flex-shrink: 0;

      .van-image {
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }

      .image-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: #f5f5f5;
        border-radius: 8px;
        color: #ccc;
      }
    }

    .part-info {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .part-name {
        font-size: 15px;
        font-weight: 500;
        color: #333;
        line-height: 1.4;
        flex: 1;
        margin-right: 12px;
      }

      .view-bom-icon {
        color: #e74c3c;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: background-color 0.2s;

        &:active {
          background-color: rgba(231, 76, 60, 0.1);
        }
      }
    }
  }
}

.video-list, .manual-list {
  // padding 由 .van-tab__panel 统一处理

  .video-item, .manual-item {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;

    .video-thumbnail, .manual-icon {
      width: 60px;
      height: 60px;
      margin-right: 12px;
      flex-shrink: 0;
      position: relative;

      .video-image, .manual-image {
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }

      .play-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }
    }

    .video-info, .manual-info {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .video-name, .manual-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        line-height: 1.4;
      }
    }
  }
}



// 数量选择弹窗样式
.quantity-popup {
  padding: 24px;
  text-align: center;

  .popup-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 24px;
  }

  .quantity-input {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
  }

  .popup-actions {
    display: flex;
    gap: 12px;

    .van-button {
      flex: 1;
      height: 40px;
    }
  }
}

// 响应式设计
@media (min-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .catalog-page {
    background-color: #1a1a1a;
  }

  .brand-item, .product-item,
  .equipment-header, .equipment-images, .parameter-section,
  .part-item, .video-item, .manual-item {
    background: #2a2a2a;

    .brand-name, .product-name,
    .equipment-title, .part-name, .video-name, .manual-name {
      color: #fff;
    }

    .equipment-code {
      color: #ccc;
    }

    .brand-arrow {
      color: #e74c3c; // 深色模式下箭头也保持主题红色
    }

    .view-bom-icon {
      color: #e74c3c; // 深色模式下BOM查看图标也保持主题红色
    }
  }

  .quantity-popup {
    background: #2a2a2a;

    .popup-title {
      color: #fff;
    }
  }
}
</style>